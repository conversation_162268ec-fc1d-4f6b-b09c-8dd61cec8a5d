/**
 * Faculty Management Styles
 * Dedicated CSS for faculty-related components and pages
 */

/* ===== FACULTY MANAGEMENT SECTION ===== */
.faculty-management {
    padding: var(--spacing-lg);
}

.faculty-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-light);
}

.faculty-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.faculty-title::before {
    content: '👨‍🏫';
    font-size: 2.5rem;
}

/* ===== FACULTY ACTION BUTTONS ===== */
.faculty-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.btn-view-faculty {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.btn-view-faculty:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
    text-decoration: none;
}

.btn-view-faculty:active {
    transform: translateY(0);
}

/* ===== FACULTY FORM STYLES ===== */
.faculty-form-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.faculty-form-header {
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.faculty-form-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.faculty-form-content {
    padding: var(--spacing-xl);
}

.faculty-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.faculty-form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* ===== FACULTY MEMBER STYLES ===== */
.faculty-member {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.faculty-member-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.faculty-member-title {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.remove-faculty {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.remove-faculty:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* ===== FACULTY BUTTONS ===== */
.btn-save-faculty {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-save-faculty:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-reset-faculty {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-reset-faculty:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

/* ===== FACULTY TABLE STYLES ===== */
.all-faculty-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.all-faculty-header {
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.all-faculty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.faculty-stats {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.all-faculty-content {
    padding: var(--spacing-lg);
}

/* ===== FACULTY EDIT MODAL ===== */
.faculty-edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.faculty-edit-modal .modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.faculty-edit-modal .modal-header {
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faculty-edit-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.faculty-edit-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.faculty-edit-modal .modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.faculty-edit-modal .modal-body {
    padding: var(--spacing-xl);
}

.faculty-edit-modal .modal-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.btn-cancel-faculty {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-cancel-faculty:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
}

/* ===== FORM STYLES ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-control {
    padding: var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .faculty-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .faculty-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .faculty-form-grid {
        grid-template-columns: 1fr;
    }

    .faculty-form-actions {
        flex-direction: column;
    }

    .faculty-edit-modal .modal-content {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .all-faculty-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .faculty-stats {
        justify-content: center;
    }

    .faculty-edit-modal .modal-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .faculty-edit-modal .btn-save-faculty,
    .faculty-edit-modal .btn-cancel-faculty {
        width: 100%;
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}
