<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">

</head>
<body class="light-theme">
    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Faculty Section -->
            <section id="faculty-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Faculty Information Management</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="searchFaculty">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                        <button class="btn btn-primary" id="addFacultyBtn">
                            <i class="fas fa-plus"></i>
                            Add Faculty Department
                        </button>
                    </div>
                </div>

                <div class="form-container">
                    <form id="facultyForm" class="admin-form">
                        <input type="hidden" id="facultyId" name="facultyId">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="facultyDepartment">Department Name *</label>
                                <input type="text" id="facultyDepartment" name="department" required>
                            </div>

                            <div class="form-group">
                                <label for="facultyHOD">HOD Name *</label>
                                <input type="text" id="facultyHOD" name="hodName" required>
                            </div>
                        </div>

                        <div class="faculty-list-section">
                            <h3>Faculty Members</h3>
                            <div id="facultyMembersList">
                                <div class="faculty-member">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>Name *</label>
                                            <input type="text" name="facultyName[]" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Subject *</label>
                                            <input type="text" name="facultySubject[]" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Email</label>
                                            <input type="email" name="facultyEmail[]">
                                        </div>
                                        <div class="form-group">
                                            <label>Phone</label>
                                            <input type="tel" name="facultyPhone[]">
                                        </div>
                                        <div class="form-group">
                                            <label>Qualification</label>
                                            <input type="text" name="facultyQualification[]">
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn btn-danger remove-faculty">Remove</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary" id="addFacultyMember">+ Add Faculty Member</button>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Faculty Info</button>
                            <button type="button" class="btn btn-secondary" id="resetFacultyForm">Reset</button>
                        </div>
                    </form>
                </div>

                <div class="data-list" id="facultyList">
                    <!-- Faculty departments will be loaded here -->
                </div>
            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    
    <script>
        // Initialize the admin panel for faculty page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'faculty';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'faculty';
                console.log('Using existing AdminPanel instance for faculty');
            }
        });
    </script>
    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>

</body>
</html>
