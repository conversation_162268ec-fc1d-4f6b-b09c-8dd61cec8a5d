/**
 * Fee Structure Management Styles
 * Dedicated CSS for fee-related components and pages
 */

/* ===== FEE MANAGEMENT SECTION ===== */
.fee-management {
    padding: var(--spacing-lg);
}

.fee-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-light);
}

.fee-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.fee-title::before {
    content: '💰';
    font-size: 2.5rem;
}

/* ===== FEE ACTION BUTTONS ===== */
.fee-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.btn-view-fees {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.btn-view-fees:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
    text-decoration: none;
}

.btn-view-fees:active {
    transform: translateY(0);
}

/* ===== FEE FORM STYLES ===== */
.fee-form-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.fee-form-header {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.fee-form-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.fee-form-content {
    padding: var(--spacing-xl);
}

.fee-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.fee-form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* ===== FEE BUTTONS ===== */
.btn-save-fee {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-save-fee:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-reset-fee {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-reset-fee:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

/* ===== FEE TABLE STYLES ===== */
.all-fees-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.all-fees-header {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.all-fees-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.fees-stats {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.all-fees-content {
    padding: var(--spacing-lg);
}

/* ===== FEE EDIT MODAL ===== */
.fee-edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.fee-edit-modal .modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.fee-edit-modal .modal-header {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fee-edit-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.fee-edit-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.fee-edit-modal .modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.fee-edit-modal .modal-body {
    padding: var(--spacing-xl);
}

.fee-edit-modal .modal-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.btn-cancel-fee {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-cancel-fee:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
}

/* ===== FORM STYLES ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-control {
    padding: var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .fee-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .fee-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .fee-form-grid {
        grid-template-columns: 1fr;
    }

    .fee-form-actions {
        flex-direction: column;
    }

    .fee-edit-modal .modal-content {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .all-fees-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .fees-stats {
        justify-content: center;
    }

    .fee-edit-modal .modal-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .fee-edit-modal .btn-save-fee,
    .fee-edit-modal .btn-cancel-fee {
        width: 100%;
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}
