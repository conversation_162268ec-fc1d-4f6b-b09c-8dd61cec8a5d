/**
 * Student Management Styles
 * Dedicated CSS for student-related components and pages
 */

/* ===== STUDENT MANAGEMENT SECTION ===== */
.student-management {
    padding: var(--spacing-lg);
}

.student-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-light);
}

.student-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.student-title::before {
    content: '🎓';
    font-size: 2.5rem;
}

/* ===== STUDENT ACTION BUTTONS ===== */
.student-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.btn-view-students {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.btn-view-students:hover {
    background: linear-gradient(135deg, #0891b2, #0e7490);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
    text-decoration: none;
}

.btn-view-students:active {
    transform: translateY(0);
}

/* ===== STUDENT FORM STYLES ===== */
.student-form-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.student-form-header {
    background: linear-gradient(135deg, #ecfeff, #cffafe);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.student-form-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.student-form-content {
    padding: var(--spacing-xl);
}

.student-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.student-form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* ===== STUDENT BUTTONS ===== */
.btn-save-student {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-save-student:hover {
    background: linear-gradient(135deg, #0891b2, #0e7490);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-reset-student {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-reset-student:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

/* ===== STUDENT TABLE STYLES ===== */
.all-students-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.all-students-header {
    background: linear-gradient(135deg, #ecfeff, #cffafe);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.all-students-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.students-stats {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.all-students-content {
    padding: var(--spacing-lg);
}

/* ===== STUDENT EDIT MODAL ===== */
.student-edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.student-edit-modal .modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
}

.student-edit-modal .modal-header {
    background: linear-gradient(135deg, #ecfeff, #cffafe);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.student-edit-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.student-edit-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.student-edit-modal .modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.student-edit-modal .modal-body {
    padding: var(--spacing-xl);
}

.student-edit-modal .modal-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.btn-cancel-student {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-cancel-student:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
}

/* ===== FORM STYLES ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-control {
    padding: var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .student-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .student-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .student-form-grid {
        grid-template-columns: 1fr;
    }

    .student-form-actions {
        flex-direction: column;
    }

    .student-edit-modal .modal-content {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .all-students-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .students-stats {
        justify-content: center;
    }

    .student-edit-modal .modal-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .student-edit-modal .btn-save-student,
    .student-edit-modal .btn-cancel-student {
        width: 100%;
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}
