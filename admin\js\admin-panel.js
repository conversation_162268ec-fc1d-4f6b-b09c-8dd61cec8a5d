// ===== ADMIN PANEL DASHBOARD JAVASCRIPT =====

class AdminPanel {
    static instance = null;

    static getInstance() {
        return AdminPanel.instance;
    }

    constructor() {
        if (AdminPanel.instance) {
            console.warn('AdminPanel instance already exists. Returning existing instance.');
            return AdminPanel.instance;
        }

        AdminPanel.instance = this;

        this.currentSection = 'dashboard';
        this.initialized = false;

        // Message queue system
        this.messageQueue = [];
        this.isShowingMessage = false;
        this.currentToast = null;
        this.messageTimeout = null;

        this.init();
    }

    async init() {
        if (this.initialized) {
            console.log('AdminPanel already initialized, skipping...');
            return;
        }

        try {
            // Setup event listeners
            this.setupEventListeners();

            // Load dashboard data
            await this.loadDashboardData();

            this.initialized = true;
            console.log('Admin panel dashboard initialized successfully');

        } catch (error) {
            console.error('Error initializing admin panel:', error);
        }
    }

    setupEventListeners() {
        // Remove existing event listeners to prevent duplicates
        this.removeEventListeners();

        // Store bound functions for later removal
        this.boundFunctions = {
            logout: () => this.logout()
        };

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.boundFunctions.logout);
        }

        // Dashboard refresh button
        const refreshDashboard = document.getElementById('refreshDashboard');
        if (refreshDashboard) {
            refreshDashboard.addEventListener('click', () => this.loadDashboardData());
        }



        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    removeEventListeners() {
        if (!this.boundFunctions) return;

        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.removeEventListener('click', this.boundFunctions.logout);
        }
    }





    // ===== DASHBOARD FUNCTIONALITY =====
    
    async loadDashboardData() {
        try {
            console.log('Loading dashboard data...');

            // Load mock statistics
            const stats = await this.loadCollectionStats();

            // Update stat cards
            document.getElementById('totalCourses').textContent = stats.courses;
            document.getElementById('totalStudents').textContent = stats.students;
            document.getElementById('totalFaculty').textContent = stats.faculty;
            document.getElementById('totalNotices').textContent = stats.notices;

            // Calculate mock changes
            await this.calculateStatChanges();

            console.log('Dashboard data loaded successfully');
        } catch (error) {
            console.error('Error in loadDashboardData:', error);
            this.showMessage('Error loading dashboard data', 'error');
        }
    }

    async loadCollectionStats() {
        // Return mock statistics for dashboard
        const stats = {
            courses: 12,
            students: 450,
            faculty: 25,
            notices: 8
        };

        console.log('Loaded mock statistics:', stats);
        return stats;
    }

    async calculateStatChanges() {
        try {
            // Mock changes data
            const mockChanges = {
                courses: '+2 this month',
                students: '+15 this month',
                faculty: '+1 this month',
                notices: '+3 this month'
            };

            const collections = ['courses', 'students', 'faculty', 'notices'];

            for (const collectionName of collections) {
                const elementId = `${collectionName}Change`;
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = mockChanges[collectionName];
                } else {
                    console.warn(`Element ${elementId} not found in DOM`);
                }
            }
        } catch (error) {
            console.error('Error calculating stat changes:', error);
        }
    }



    // ===== MESSAGE SYSTEM =====

    showMessage(message, type = 'info', duration = 3000) {
        // Add message to queue
        this.messageQueue.push({ message, type, duration });

        // Process queue if not already showing a message
        if (!this.isShowingMessage) {
            this.processMessageQueue();
        }
    }

    processMessageQueue() {
        if (this.messageQueue.length === 0) {
            this.isShowingMessage = false;
            return;
        }

        this.isShowingMessage = true;
        const { message, type, duration } = this.messageQueue.shift();

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close">&times;</button>
        `;

        // Add to DOM
        document.body.appendChild(toast);
        this.currentToast = toast;

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Close button handler
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.hideCurrentToast());

        // Auto-hide after duration
        this.messageTimeout = setTimeout(() => {
            this.hideCurrentToast();
        }, duration);
    }

    hideCurrentToast() {
        if (this.currentToast) {
            this.currentToast.classList.remove('show');
            setTimeout(() => {
                if (this.currentToast && this.currentToast.parentNode) {
                    this.currentToast.parentNode.removeChild(this.currentToast);
                }
                this.currentToast = null;
                // Process next message in queue
                this.processMessageQueue();
            }, 300);
        }

        if (this.messageTimeout) {
            clearTimeout(this.messageTimeout);
            this.messageTimeout = null;
        }
    }

    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // ===== UTILITY METHODS =====

    logout() {
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminUser');
            window.location.href = '../index.html';
        }
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (!window.adminPanel) {
        window.adminPanel = new AdminPanel();
    }
});
