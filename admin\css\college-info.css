/**
 * College Information Management Styles
 * Dedicated CSS for college info-related components and pages
 */

/* ===== COLLEGE INFO MANAGEMENT SECTION ===== */
.college-info-management {
    padding: var(--spacing-lg);
}

.college-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-light);
}

.college-info-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.college-info-title::before {
    content: '🏛️';
    font-size: 2.5rem;
}

/* ===== COLLEGE INFO ACTION BUTTONS ===== */
.college-info-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.btn-view-college-info {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.btn-view-college-info:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
    text-decoration: none;
}

.btn-view-college-info:active {
    transform: translateY(0);
}

/* ===== COLLEGE INFO FORM STYLES ===== */
.college-info-form-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
}

.college-info-form-header {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.college-info-form-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.college-info-form-content {
    padding: var(--spacing-xl);
}

.college-info-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.college-info-form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

/* ===== COLLEGE INFO BUTTONS ===== */
.btn-save-college-info {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-save-college-info:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-reset-college-info {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-reset-college-info:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
    transform: translateY(-1px);
}

/* ===== COLLEGE INFO TABLE STYLES ===== */
.all-college-info-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.all-college-info-header {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.all-college-info-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.college-info-stats {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.all-college-info-content {
    padding: var(--spacing-lg);
}

/* ===== COLLEGE INFO EDIT MODAL ===== */
.college-info-edit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.college-info-edit-modal .modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
}

.college-info-edit-modal .modal-header {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.college-info-edit-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.college-info-edit-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.college-info-edit-modal .modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.college-info-edit-modal .modal-body {
    padding: var(--spacing-xl);
}

.college-info-edit-modal .modal-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.btn-cancel-college-info {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-cancel-college-info:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
}

/* ===== FORM STYLES ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-control {
    padding: var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .college-info-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .college-info-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .college-info-form-grid {
        grid-template-columns: 1fr;
    }

    .college-info-form-actions {
        flex-direction: column;
    }

    .college-info-edit-modal .modal-content {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .all-college-info-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .college-info-stats {
        justify-content: center;
    }

    .college-info-edit-modal .modal-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .college-info-edit-modal .btn-save-college-info,
    .college-info-edit-modal .btn-cancel-college-info {
        width: 100%;
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}
