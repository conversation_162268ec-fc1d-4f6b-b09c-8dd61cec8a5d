<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View All College Info - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/college-info.css">
</head>
<body class="light-theme">
    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>

            <!-- View College Info Section -->
            <section id="view-college-info-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title college-info-title">View All College Information</h1>
                    <div class="section-actions college-info-actions">
                        <button class="btn-import-courses" id="importCollegeInfoBtn">
                            <i class="fas fa-upload"></i>
                            Import College Info
                        </button>
                        <button class="btn-export-courses" id="exportCollegeInfoBtn">
                            <i class="fas fa-download"></i>
                            Export College Info
                        </button>
                        <a href="college-info.html" class="btn-add-course">
                            <i class="fas fa-plus"></i>
                            Add New College Info
                        </a>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="all-courses-container">
                        <div class="all-courses-header">
                            <h3 class="all-courses-title">
                                <i class="fas fa-university"></i>
                                College Information Management
                            </h3>
                            <div class="courses-stats">
                                <span class="stat-item">
                                    <i class="fas fa-info-circle"></i>
                                    Total: <span id="totalCollegeInfoCount">0</span>
                                </span>
                            </div>
                        </div>
                        <div class="all-courses-content">
                            <div id="collegeInfoTable"></div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

    <!-- Edit College Info Modal -->
    <div id="editCollegeInfoModal" class="college-info-edit-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">✏️ Edit College Information</h3>
                <button class="modal-close" onclick="closeEditCollegeInfoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editCollegeInfoForm">
                    <input type="hidden" id="editCollegeInfoId">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editCollegeName">College Name:</label>
                            <input type="text" id="editCollegeName" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editEstablishedYear">Established Year:</label>
                            <input type="number" id="editEstablishedYear" class="form-control" min="1800" max="2030" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editAffiliation">Affiliation:</label>
                            <input type="text" id="editAffiliation" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editAccreditation">Accreditation:</label>
                            <input type="text" id="editAccreditation" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editAddress">Address:</label>
                        <textarea id="editAddress" class="form-control" rows="3" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editContactPhone">Contact Phone:</label>
                            <input type="tel" id="editContactPhone" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editContactEmail">Contact Email:</label>
                            <input type="email" id="editContactEmail" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editWebsite">Website:</label>
                            <input type="url" id="editWebsite" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="editPrincipalName">Principal Name:</label>
                            <input type="text" id="editPrincipalName" class="form-control" required>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn-cancel-college-info" onclick="closeEditCollegeInfoModal()">Cancel</button>
                        <button type="submit" class="btn-save-college-info">Update College Info</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- PapaParse for CSV handling -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>

    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>

    <!-- Courses JavaScript (includes dropdown functionality) -->
    <script src="js/courses.js"></script>

    <!-- College Info JavaScript (handles both add and view functionality) -->
    <script src="js/college-info.js"></script>

    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>

    <script>
        // Initialize view college info page functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('View College Info page loaded');

            // Initialize admin panel
            if (window.AdminPanel) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'view-college-info';
            }

            // Initialize view college info functionality
            if (window.ViewCollegeInfoPage) {
                window.ViewCollegeInfoPage.initializeViewCollegeInfoPage();
            }
        });

        // Global functions for college info operations
        function editCollegeInfo(collegeInfoId) {
            if (window.viewCollegeInfoManager) {
                window.viewCollegeInfoManager.editCollegeInfo(collegeInfoId);
            }
        }

        function deleteCollegeInfo(collegeInfoId) {
            if (window.viewCollegeInfoManager) {
                window.viewCollegeInfoManager.deleteCollegeInfo(collegeInfoId);
            }
        }

        function closeEditCollegeInfoModal() {
            document.getElementById('editCollegeInfoModal').style.display = 'none';
        }
    </script>
</body>
</html>
