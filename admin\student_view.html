<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View All Students - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/student.css">
    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">

</head>
<body class="light-theme">
    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>


        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>

            <!-- View Students Section -->
            <section id="view-students-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title student-title">View All Students</h1>
                    <div class="section-actions student-actions">
                        <button class="btn-import-courses" id="importStudentsBtn">
                            <i class="fas fa-upload"></i>
                            Import Students
                        </button>
                        <button class="btn-export-courses" id="exportStudentsBtn">
                            <i class="fas fa-download"></i>
                            Export Students
                        </button>
                        <a href="students.html" class="btn-add-course">
                            <i class="fas fa-plus"></i>
                            Add New Student
                        </a>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="all-courses-container">
                        <div class="all-courses-header">
                            <h3 class="all-courses-title">
                                <i class="fas fa-users"></i>
                                Student Management
                            </h3>
                            <div class="courses-stats">
                                <span class="stat-item">
                                    <i class="fas fa-user-graduate"></i>
                                    Total: <span id="totalStudentsCount">0</span>
                                </span>
                            </div>
                        </div>
                        <div class="all-courses-content">
                            <div id="studentsTable"></div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

    <!-- Edit Student Modal -->
    <div id="editStudentModal" class="student-edit-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">✏️ Edit Student Information</h3>
                <button class="modal-close" onclick="closeEditStudentModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editStudentForm">
                    <input type="hidden" id="editStudentId">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editStudentName">Student Name:</label>
                            <input type="text" id="editStudentName" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editRollNumber">Roll Number:</label>
                            <input type="text" id="editRollNumber" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editStudentCourse">Course:</label>
                            <select id="editStudentCourse" class="form-control" required>
                                <option value="">-- Select Course --</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editStudentSemester">Semester:</label>
                            <select id="editStudentSemester" class="form-control" required>
                                <option value="">-- Select Semester --</option>
                                <option value="1">1st Semester</option>
                                <option value="2">2nd Semester</option>
                                <option value="3">3rd Semester</option>
                                <option value="4">4th Semester</option>
                                <option value="5">5th Semester</option>
                                <option value="6">6th Semester</option>
                                <option value="7">7th Semester</option>
                                <option value="8">8th Semester</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editStudentEmail">Email:</label>
                            <input type="email" id="editStudentEmail" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editStudentPhone">Phone:</label>
                            <input type="tel" id="editStudentPhone" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="editDateOfBirth">Date of Birth:</label>
                            <input type="date" id="editDateOfBirth" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="editAdmissionDate">Admission Date:</label>
                            <input type="date" id="editAdmissionDate" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="editStudentAddress">Address:</label>
                        <textarea id="editStudentAddress" class="form-control" rows="3" required></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn-cancel-student" onclick="closeEditStudentModal()">Cancel</button>
                        <button type="submit" class="btn-save-student">Update Student</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- PapaParse for CSV handling -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>

    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>

    <!-- Courses JavaScript (includes dropdown functionality) -->
    <script src="js/courses.js"></script>

    <!-- Student JavaScript (handles both add and view functionality) -->
    <script src="js/student.js"></script>

    <script>
        // Initialize view students page functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('View Students page loaded');

            // Initialize admin panel
            if (window.AdminPanel) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'view-students';
            }

            // Initialize view students functionality
            if (window.ViewStudentsPage) {
                window.ViewStudentsPage.initializeViewStudentsPage();
            }
        });

        // Global functions for student operations
        function editStudent(studentId) {
            if (window.viewStudentsManager) {
                window.viewStudentsManager.editStudent(studentId);
            }
        }

        function deleteStudent(studentId) {
            if (window.viewStudentsManager) {
                window.viewStudentsManager.deleteStudent(studentId);
            }
        }

        function closeEditStudentModal() {
            document.getElementById('editStudentModal').style.display = 'none';
        }
    </script>
    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>

</body>
</html>
