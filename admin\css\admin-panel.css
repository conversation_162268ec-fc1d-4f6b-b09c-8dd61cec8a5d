/* ===== ADMIN PANEL DASHBOARD STYLES ===== */

/* ===== CSS VARIABLES FOR THEMING ===== */
:root {
    /* Light Theme Colors */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-tertiary: #94a3b8;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e0;
    --border-dark: #94a3b8;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Layout */
}

/* Dark Theme Variables */
.dark-theme {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #9ca3af;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --bg-accent: #475569;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e0;
    --text-tertiary: #94a3b8;
    --text-inverse: #1e293b;

    --border-light: #334155;
    --border-medium: #475569;
    --border-dark: #64748b;
}

/* ===== BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== LAYOUT STRUCTURE ===== */
.admin-container {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}







/* ===== MAIN CONTENT ===== */
.main-content {
    padding: var(--spacing-xl);
    min-height: 100vh;
    margin-top: 70px; /* Account for fixed navbar */
}

/* ===== LOADING INDICATOR ===== */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== CONTENT SECTIONS ===== */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: var(--bg-accent);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.btn-outline:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-dark);
}

/* ===== DASHBOARD STYLES ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-light);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-inverse);
    flex-shrink: 0;
}

.stat-icon.courses {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-icon.students {
    background: linear-gradient(135deg, #10b981, #047857);
}

.stat-icon.faculty {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.notices {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-sm) 0;
    font-weight: 500;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change i {
    font-size: 0.7rem;
}

/* ===== DASHBOARD SECTIONS ===== */
.dashboard-section {
    margin-bottom: var(--spacing-xl);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.section-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.card-title i {
    color: var(--primary-color);
}

.card-content {
    padding: var(--spacing-lg);
}

/* ===== QUICK ACTIONS ===== */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.action-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--text-inverse);
    flex-shrink: 0;
}

.action-icon.courses {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.action-icon.students {
    background: linear-gradient(135deg, #10b981, #047857);
}

.action-icon.notices {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.action-icon.timetables {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-content {
    flex: 1;
}

.action-title {
    display: block;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.action-desc {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* ===== MODAL STYLES ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* ===== FORM STYLES ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
    color: var(--text-tertiary);
}

.password-strength {
    margin-top: var(--spacing-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* ===== TOAST MESSAGES ===== */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    max-width: 400px;
    z-index: 3000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toast-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--danger-color);
}

.toast-warning {
    border-left: 4px solid var(--warning-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

.toast-success .toast-content i {
    color: var(--success-color);
}

.toast-error .toast-content i {
    color: var(--danger-color);
}

.toast-warning .toast-content i {
    color: var(--warning-color);
}

.toast-info .toast-content i {
    color: var(--info-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }

    .section-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .section-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-bottom: var(--spacing-sm);
    }

    .modal {
        width: 95%;
        margin: var(--spacing-md);
    }

    .modal-footer {
        flex-direction: column;
    }

    .toast {
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }
}

@media (max-width: 480px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .action-card {
        padding: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-lg);
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-sm {
    margin-bottom: var(--spacing-sm);
}

.mb-md {
    margin-bottom: var(--spacing-md);
}

.mb-lg {
    margin-bottom: var(--spacing-lg);
}

.mt-0 {
    margin-top: 0;
}

.mt-sm {
    margin-top: var(--spacing-sm);
}

.mt-md {
    margin-top: var(--spacing-md);
}

.mt-lg {
    margin-top: var(--spacing-lg);
}

.hidden {
    display: none;
}

.visible {
    display: block;
}

.flex {
    display: flex;
}

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.gap-sm {
    gap: var(--spacing-sm);
}

.gap-md {
    gap: var(--spacing-md);
}

.gap-lg {
    gap: var(--spacing-lg);
}
