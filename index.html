<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitBot - Your Smart College Assistant | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="assets/favicon.ico">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/chatbot-library-theme.css">

    <!-- Mock authentication system -->
</head>
<body>
    <!-- Full Screen Chatbot Interface -->
    <div class="chatbot-app">
        <!-- Chat Header -->
        <header class="chat-header">
            <div class="header-left">
                <div class="bot-avatar">🤖</div>
                <div class="bot-info">
                    <h1 class="bot-name">BITbot Assistant</h1>
                    <span class="bot-status">
                    <span class="online-dot"></span>
                    Online
                </span>
                </div>
            </div>
            <div class="header-right">
                <button class="theme-toggle" id="themeToggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
                <!-- Login/Logout button - dynamically shown based on auth state -->
                <button class="login-btn" id="loginBtn" style="margin-left: 12px; padding: 8px 16px; background: #000; color: #fff; border: 1px solid #000; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 6px;">
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                        <polyline points="10,17 15,12 10,7"></polyline>
                        <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                    Login
                </button>
                <button class="logout-btn" id="logoutBtn" style="margin-left: 12px; padding: 8px 16px; background: #000; color: #fff; border: 1px solid #000; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500; display: none;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 6px;">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                        <polyline points="16,17 21,12 16,7"></polyline>
                        <line x1="21" y1="12" x2="9" y2="12"></line>
                    </svg>
                    Logout
                </button>
            </div>
        </header>

        <!-- Chat Messages Area -->
        <main class="chat-messages" id="chatMessages">
            <!-- Welcome Message -->
            <div class="message bot-message" id="welcomeMessage">
                <div class="message-bubble">
                    <div class="message-content">
                        <p>Hi! I'm BitBot, your smart college assistant for <strong>Bhagwant Institute of Technology</strong>. I can help you with:</p>
                        <div class="suggestion-chips">
                            <button class="chip" data-query="What is today's timetable?">📅 Today's timetable</button>
                            <button class="chip" data-query="What are the class timings?">⏰ Class timings</button>
                            <button class="chip" data-query="What is the course fee for BCA?">💰 Course fees</button>
                            <button class="chip" data-query="Who is the HOD of the IT department?">👨‍🏫 Faculty information</button>
                            <button class="chip" data-query="What is my fee due?">🔒 My fee status (Login required)</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Typing Indicator -->
        <div class="typing-indicator" id="typingIndicator">
            <div class="message bot-message">
                <div class="message-bubble">
                    <div class="typing-animation">
                        <span class="typing-text">BitBot is typing</span>
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Input Section -->
        <footer class="chat-input-section">
            <div class="input-container">
                <div class="input-wrapper">
                    <input
                        type="text"
                        id="chatInput"
                        placeholder="Ask me anything..."
                        class="chat-input"
                        autocomplete="off"
                    >
                    <button id="sendBtn" class="send-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22,2 15,22 11,13 2,9"></polygon>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Footer -->
            <div class="app-footer">
                <p>&copy; 2025 BitBot | Developed by Akash Prajapati, BCA 3rd Year – Bhagwant Institute of Technology</p>
            </div>
        </footer>
    </div>

    <!-- Login Popup Modals -->
    <div id="loginModal" class="login-modal" style="display: none;">
        <div class="modal-overlay" onclick="closeLoginPopup()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Login</h2>
                <button class="modal-close" onclick="closeLoginPopup()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Student Login Form -->
                <div id="studentLoginForm" class="login-form-container" style="display: none;">
                    <div class="login-header">
                        <div class="bot-avatar">🤖</div>
                        <h3>Student Portal</h3>
                        <p>Bhagwant Institute of Technology</p>
                    </div>
                    <form id="popupStudentForm">
                        <div class="form-group">
                            <label for="popupStudentEmail">Gmail Address</label>
                            <input type="email" id="popupStudentEmail" placeholder="Enter your Gmail address" required>
                        </div>
                        <div class="form-group">
                            <label for="popupStudentDob">Date of Birth</label>
                            <input type="date" id="popupStudentDob" required>
                        </div>
                        <button type="submit" class="login-btn">
                            <span class="btn-text">Student Sign In</span>
                            <div class="btn-loader" style="display: none;">
                                <div class="spinner"></div>
                            </div>
                        </button>
                    </form>
                    <div class="login-footer">
                        <p>Need admin access? <a href="#" onclick="switchToAdminLogin()">Admin Login</a></p>
                    </div>
                </div>

                <!-- Admin Login Form -->
                <div id="adminLoginForm" class="login-form-container" style="display: none;">
                    <div class="login-header">
                        <div class="bot-avatar">🛡️</div>
                        <h3>Admin Panel</h3>
                        <p>Authorized Personnel Only</p>
                    </div>
                    <form id="popupAdminForm">
                        <div class="form-group">
                            <label for="popupAdminEmail">Email Address</label>
                            <input type="email" id="popupAdminEmail" placeholder="Enter admin email" required>
                        </div>
                        <div class="form-group">
                            <label for="popupAdminPassword">Password</label>
                            <input type="password" id="popupAdminPassword" placeholder="Enter password" required>
                        </div>
                        <button type="submit" class="login-btn admin-btn">
                            <span class="btn-text">Admin Sign In</span>
                            <div class="btn-loader" style="display: none;">
                                <div class="spinner"></div>
                            </div>
                        </button>
                    </form>
                    <div class="login-footer">
                        <p>Student access? <a href="#" onclick="switchToStudentLogin()">Student Login</a></p>
                    </div>
                </div>

                <!-- Error Message -->
                <div id="popupErrorMessage" class="error-message" style="display: none;">
                    <span class="error-text" id="popupErrorText"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>


    <script src="admin/js/auth.js"></script>
    <script src="js/script.js"></script>
    <script>
        // Initialize public chatbot (no authentication required)
        // Authentication is only needed for sensitive queries
        document.addEventListener('DOMContentLoaded', () => {
            initializePublicChatbot();

            // Ensure popup functions are available globally
            if (typeof openLoginPopup === 'function') {
                window.openLoginPopup = openLoginPopup;
                window.closeLoginPopup = closeLoginPopup;
                window.switchToAdminLogin = switchToAdminLogin;
                window.switchToStudentLogin = switchToStudentLogin;
            }
        });
    </script>
</body>
</html>
