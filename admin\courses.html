<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Courses Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/courses.css">
</head>
<body class="light-theme">
    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Courses Section -->
            <section id="courses-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title course-title">Course Management</h1>
                    <div class="section-actions course-actions">
                        <button class="btn-import-courses" id="bulkImportBtn">
                            <i class="fas fa-upload"></i>
                            Bulk Import CSV
                        </button>
                        <button class="btn-export-courses" id="bulkExportBtn">
                            <i class="fas fa-download"></i>
                            Bulk Export CSV
                        </button>
                        <a href="courses_view.html" class="btn-view-courses">
                            <i class="fas fa-eye"></i>
                            View All Courses
                        </a>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="course-form-container">
                        <div class="course-form-header">
                            <h3 class="course-form-title">
                                <i class="fas fa-plus-circle"></i>
                                Course Information
                            </h3>
                        </div>
                        <div class="course-form-content">
                            <form id="courseForm" class="modern-form">
                                <input type="hidden" id="courseId" name="courseId">

                                <div class="course-form-grid">
                                    <div class="course-form-group">
                                        <label class="course-form-label" for="courseName">📚 Course Name *</label>
                                        <input class="course-form-input" type="text" id="courseName" name="courseName" placeholder="e.g., BCA, B.Tech" required minlength="2" maxlength="100">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="department">🏢 Department *</label>
                                        <input class="course-form-input" type="text" id="department" name="department" placeholder="e.g., Computer Applications, Computer Science" required minlength="2" maxlength="100">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="courseAffiliation">🏛️ Course Affiliation *</label>
                                        <input class="course-form-input" type="text" id="courseAffiliation" name="courseAffiliation" placeholder="e.g., AKTU, CCSU" required minlength="2" maxlength="50">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="duration">⏱️ Duration *</label>
                                        <input class="course-form-input" type="text" id="duration" name="duration" placeholder="e.g., 3 years" required minlength="1" maxlength="20">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="totalSeats">👥 Total Seats *</label>
                                        <input class="course-form-input" type="number" id="totalSeats" name="totalSeats" placeholder="e.g., 60" required min="1" max="1000">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="feeStructure">💰 Fee Structure (₹ per semester) *</label>
                                        <input class="course-form-input" type="text" id="feeStructure" name="feeStructure" placeholder="e.g., ₹25,000" required minlength="1" maxlength="50">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="otherFee">➕ Other Fee (Optional)</label>
                                        <input class="course-form-input" type="text" id="otherFee" name="otherFee" placeholder="e.g., ₹2,000 stationary" maxlength="100">
                                    </div>

                                    <div class="course-form-group" style="grid-column: 1 / -1;">
                                        <label class="course-form-label" for="scholarshipOpportunities">🎓 Scholarship Opportunities</label>
                                        <textarea class="course-form-textarea" id="scholarshipOpportunities" name="scholarshipOpportunities" rows="3" placeholder="e.g., UP Government Scholarship / Merit-based / None" maxlength="500"></textarea>
                                    </div>

                                    <div class="course-form-group" style="grid-column: 1 / -1;">
                                        <label class="course-form-label" for="admissionEligibility">📋 Admission Eligibility</label>
                                        <textarea class="course-form-textarea" id="admissionEligibility" name="admissionEligibility" rows="3" placeholder="e.g., 12th Pass with 45% marks (PCM/Arts/Commerce)" maxlength="500"></textarea>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="hodName">👨‍💼 HOD Name *</label>
                                        <input class="course-form-input" type="text" id="hodName" name="hodName" placeholder="e.g., Dr. Ajay Singh" required minlength="2" maxlength="100">
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="counsellor">👩‍🏫 Course Counsellor</label>
                                        <input class="course-form-input" type="text" id="counsellor" name="counsellor" placeholder="e.g., Ms. Urvashi Chaudhary" maxlength="100">
                                    </div>
                                </div>

                                <div class="course-form-actions">
                                    <button type="submit" class="btn-save-course">
                                        <i class="fas fa-save"></i>
                                        Save Course
                                    </button>
                                    <button type="button" class="btn-reset-course" id="resetCourseBtn">
                                        <i class="fas fa-undo"></i>
                                        Reset Form
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>


                </div>
            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- PapaParse JS for CSV handling -->
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    <script src="js/courses.js"></script>

    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>

    <script>
        // Initialize the admin panel for courses page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'courses';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'courses';
                console.log('Using existing AdminPanel instance for courses');
            }

            // Handle edit parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('edit')) {
                // Wait for course manager to be ready
                setTimeout(() => {
                    if (window.courseManager) {
                        window.courseManager.populateFormFromParams(urlParams);
                    }
                }, 500);
            }
        });
    </script>
</body>
</html>
