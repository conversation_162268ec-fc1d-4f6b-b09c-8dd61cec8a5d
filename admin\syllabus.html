<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syllabus Management - BitBot Admin | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Quill.js CSS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">
    <link rel="stylesheet" href="css/courses.css">
    <link rel="stylesheet" href="css/syllabus.css">
    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">

</head>
<body class="light-theme">
    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>

            <!-- Syllabus Section -->
            <section id="syllabus-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title syllabus-title">Syllabus Management</h1>
                    <div class="section-actions course-actions">
                        <button class="btn-import-courses" id="importSyllabusBtn">
                            <i class="fas fa-upload"></i>
                            Import Syllabus
                        </button>
                        <a href="syllabus_view.html" class="btn-view-courses">
                            <i class="fas fa-eye"></i>
                            View All Syllabus
                        </a>
                    </div>
                </div>

                <div class="content-grid full-width">
                    <div class="course-form-container">
                        <div class="course-form-header">
                            <h3 class="course-form-title">
                                Syllabus Information
                            </h3>
                        </div>
                        <div class="course-form-content">
                            <form id="syllabusForm" class="modern-form">
                                <input type="hidden" id="syllabusId" name="syllabusId">

                                <!-- Course and Semester/Year Section -->
                                <div class="course-form-grid">
                                    <div class="course-form-group">
                                        <label class="course-form-label" for="syllabusCourse">Course *</label>
                                        <input class="course-form-input" type="text" id="syllabusCourse" name="course" placeholder="e.g., BCA, B.Tech CSE" required>
                                        <div class="course-suggestions" id="courseSuggestions" style="display: none;"></div>
                                    </div>

                                    <div class="course-form-group">
                                        <label class="course-form-label" for="syllabusSemester">Semester/Year *</label>
                                        <input class="course-form-input" type="text" id="syllabusSemester" name="semester" placeholder="e.g., 1st Semester, 2nd Year" required>
                                    </div>
                                </div>

                                <!-- Dynamic Subjects Section -->
                                <div class="subjects-section">
                                    <div class="subjects-header">
                                        <h4 class="subjects-title">Subjects</h4>
                                        <button type="button" class="btn-add-subject" id="addSubjectBtn">
                                            <i class="fas fa-plus"></i>
                                            Add Subject
                                        </button>
                                    </div>

                                    <div id="subjectsContainer">
                                        <!-- Initial subject entry -->
                                        <div class="subject-entry" data-subject-index="0">
                                            <div class="subject-header">
                                                <h5 class="subject-number">Subject 1</h5>
                                                <button type="button" class="btn-remove-subject" onclick="removeSubject(0)" style="display: none;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="subject-fields">
                                                <div class="course-form-grid">
                                                    <div class="course-form-group">
                                                        <label class="course-form-label" for="SubjectName_0">Subject Name *</label>
                                                        <input class="course-form-input" type="text" id="SubjectName_0" name="subjects[0][name]" placeholder="e.g., Data Structures" required>
                                                    </div>
                                                    <div class="course-form-group">
                                                        <label class="course-form-label" for="SubjectCode_0">Subject Code *</label>
                                                        <input class="course-form-input" type="text" id="SubjectCode_0" name="subjects[0][code]" placeholder="e.g., CS101" required>
                                                    </div>
                                                </div>
                                                <div class="course-form-grid">
                                                    <div class="course-form-group">
                                                        <label class="course-form-label" for="internalExternalMarks_0">Internal/External Marks *</label>
                                                        <input class="course-form-input" type="text" id="internalExternalMarks_0" name="subjects[0][marks]" placeholder="e.g., 30/70, 40/60" required>
                                                    </div>
                                                    <div class="course-form-group">
                                                        <label class="course-form-label" for="credit_0">Credit *</label>
                                                        <input class="course-form-input" type="number" id="credit_0" name="subjects[0][credits]" placeholder="e.g., 3, 4" min="1" max="10" required>
                                                    </div>
                                                </div>
                                                <div class="course-form-group">
                                                    <label class="course-form-label" for="syllabusContent_0">Syllabus Content *</label>
                                                    <div id="syllabusContent_0" class="quill-editor" style="height: 200px;"></div>
                                                    <input type="hidden" name="subjects[0][content]" id="syllabusContent_0_hidden" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Reference Books Section -->
                                <div class="course-form-group">
                                    <label class="course-form-label" for="referenceBooks">Reference Books</label>
                                    <textarea class="course-form-textarea" id="referenceBooks" name="referenceBooks" placeholder="List reference books for all subjects..." rows="4"></textarea>
                                </div>

                                <div class="course-form-actions">
                                    <button type="submit" class="btn-save-course">
                                        <i class="fas fa-save"></i>
                                        Save Syllabus
                                    </button>
                                    <button type="button" class="btn-reset-course" id="resetSyllabusBtn">
                                        <i class="fas fa-undo"></i>
                                        Reset Form
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </main>

    <!-- JavaScript Libraries -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>

    <!-- PapaParse JS for CSV handling -->
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>

    <!-- Quill.js -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>



    <!-- Admin Panel JavaScript -->
    <script src="js/admin-panel.js"></script>
    <script src="js/courses.js"></script>
    <script src="js/syllabus.js"></script>

    <script>
        // Initialize the admin panel for syllabus page
        document.addEventListener('DOMContentLoaded', function() {
            // Use singleton pattern to prevent duplicate instances
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'syllabus';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                window.adminPanel.currentPage = 'syllabus';
                console.log('Using existing AdminPanel instance for syllabus');
            }

            // Initialize syllabus page functionality
            if (window.SyllabusPage && window.SyllabusPage.initializeSyllabusPage) {
                window.SyllabusPage.initializeSyllabusPage();
            }

            // Handle edit parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('edit')) {
                // Wait for syllabus manager to be ready
                setTimeout(() => {
                    if (window.syllabusManager) {
                        window.syllabusManager.populateFormFromParams(urlParams);
                    }
                }, 500);
            }
        });
    </script>
    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>

</body>
</html>