<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - BitBot | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="../assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="../assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Tabulator CSS -->
    <link href="https://unpkg.com/tabulator-tables@6.2.1/dist/css/tabulator.min.css" rel="stylesheet">

    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Navbar CSS -->
    <link rel="stylesheet" href="css/navside.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-panel.css">
    <link rel="stylesheet" href="css/library-theme.css">

</head>
<body class="light-theme">


    <!-- Dynamic Navbar Container -->
    <div id="navbar"></div>

        <!-- Main Content Area -->
        <main class="main-content" id="mainContent">
            <!-- Loading Indicator -->
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="spinner"></div>
                <span>Loading...</span>
            </div>



            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="section-header">
                    <h1 class="section-title">Dashboard Overview</h1>
                    <div class="section-actions">
                        <button class="btn btn-outline" id="refreshDashboard">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-icon courses">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalCourses">0</h3>
                            <p class="stat-label">Total Courses</p>
                            <div class="stat-change positive" id="coursesChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-icon students">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalStudents">0</h3>
                            <p class="stat-label">Total Students</p>
                            <div class="stat-change positive" id="studentsChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-icon faculty">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalFaculty">0</h3>
                            <p class="stat-label">Faculty Members</p>
                            <div class="stat-change positive" id="facultyChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-icon notices">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number" id="totalNotices">0</h3>
                            <p class="stat-label">Active Notices</p>
                            <div class="stat-change positive" id="noticesChange">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0 this month</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-section" data-aos="fade-up" data-aos-delay="500">
                    <div class="section-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="quick-actions-grid">
                                <a href="courses.html" class="action-card">
                                    <div class="action-icon courses">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Course</span>
                                        <span class="action-desc">Create new course</span>
                                    </div>
                                </a>

                                <a href="students.html" class="action-card">
                                    <div class="action-icon students">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Student</span>
                                        <span class="action-desc">Register new student</span>
                                    </div>
                                </a>

                                <a href="notices.html" class="action-card">
                                    <div class="action-icon notices">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Post Notice</span>
                                        <span class="action-desc">Create announcement</span>
                                    </div>
                                </a>

                                <a href="timetables.html" class="action-card">
                                    <div class="action-icon timetables">
                                        <i class="fas fa-calendar-plus"></i>
                                    </div>
                                    <div class="action-content">
                                        <span class="action-title">Add Timetable</span>
                                        <span class="action-desc">Schedule classes</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">



                </div>
            </section>
        </main>

    <!-- Modal Container -->
    <div id="modalContainer"></div>



    <!-- Scripts -->
    <!-- Tabulator JS -->
    <script type="text/javascript" src="https://unpkg.com/tabulator-tables@6.2.1/dist/js/tabulator.min.js"></script>

    <!-- Toastify JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.1/dist/sweetalert2.all.min.js"></script>


    <script src="js/auth.js"></script>
    <script src="js/admin-panel.js"></script>

    <!-- Navbar JavaScript -->
    <script src="js/navside.js"></script>
    <script>
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Admin panel DOM loaded, checking authentication...');

            // Initialize navbar manager first
            if (typeof NavbarManager !== 'undefined') {
                window.navbarManager = new NavbarManager();
                console.log('NavbarManager initialized');
            } else {
                console.error('NavbarManager not found');
            }

            // Wait a bit for initialization
            await new Promise(resolve => setTimeout(resolve, 500));

            // Initialize the admin panel (singleton pattern)
            if (!AdminPanel.getInstance()) {
                window.adminPanel = new AdminPanel();
                window.adminPanel.currentPage = 'dashboard';
            } else {
                window.adminPanel = AdminPanel.getInstance();
                console.log('Using existing AdminPanel instance');
            }

            // Protect this page and initialize logout
            if (typeof protectPage === 'function') {
                console.log('Calling protectPage...');
                await protectPage();
                console.log('protectPage completed');
            } else {
                console.error('protectPage function not found');
            }
        });
    </script>
</body>
</html>
